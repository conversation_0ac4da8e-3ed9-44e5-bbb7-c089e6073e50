import type { Block } from "payload";

export const ConcertArchiveBlockConfig: Block = {
	slug: "concert-archive",
	interfaceName: "ConcertArchiveBlock",
	fields: [
		{
			name: "populateBy",
			type: "select",
			defaultValue: "all",
			options: [
				{
					label: "All",
					value: "all",
				},
				{
					label: "Nächste Konzerte",
					value: "upcoming",
				},
				{
					label: "Individual Selection",
					value: "selection",
				},
			],
		},
		{
			name: "limit",
			type: "number",
			admin: {
				condition: (_, siblingData) => siblingData.populateBy === "all",
				step: 1,
			},
			defaultValue: 3,
			label: "Limit",
		},
		{
			name: "infinite",
			type: "checkbox",
			admin: {
				condition: (_, siblingData) => siblingData.populateBy === "all",
			},
			label: "Infinite Scroll",
		},
		{
			name: "selectedDocs",
			type: "relationship",
			admin: {
				condition: (_, siblingData) => siblingData.populateBy === "selection",
			},
			hasMany: true,
			label: "Selection",
			relationTo: ["concerts"],
		},
	],
	labels: {
		plural: "Konzert Archives",
		singular: "Konzert Archive",
	},
};
