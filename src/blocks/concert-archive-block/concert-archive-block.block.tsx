import type { FC } from "react";
import type {
	ConcertArchiveBlock as IConcertArchiveBlockProps,
	Concert,
} from "@/payload-types";
import configPromise from "@payload-config";
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@/components/accordion";
import { getPayload } from "payload";
import RichText from "@/components/render/render-rich-text";
import { Media } from "@/components/render/render-media";

export const ConcertArchiveBlock: FC<IConcertArchiveBlockProps> = async ({
	limit: limitProp = 1,
	populateBy,
	selectedDocs,
	infinite,
}) => {
	const payload = await getPayload({ config: configPromise });

	if (populateBy === "upcoming") {
		const concert = await payload
			.find({
				collection: "concerts",
				depth: 1,
				where: {
					"dates.date": {
						greater_than_equal: new Date().toISOString(),
					},
				},
				sort: "dates.date",
				limit: 1,
			})
			.then((res) => res.docs[0]);

		if (!concert) {
			console.warn("No upcoming concert found");
			return null;
		}

		return (
			<section className="layout-block">
				<div className="default-grid">
					<div className="col-start-3 col-span-8">
						<Media resource={concert.image} className="mb-8" />
						<div className="mb-8">
							<h3>
								<strong>{concert.title}</strong>
							</h3>
							<p className="h3">{concert.subline}</p>
						</div>
						<div className="grid [grid-template-columns:max-content_1fr] gap-x-4 gap-y-2">
							<p>
								<strong>WANN:</strong>
							</p>
							<p>{concert.formattedDateString}</p>
							<p>
								<strong>WO:</strong>
							</p>
							<p>{concert.where}</p>
						</div>
					</div>
				</div>
			</section>
		);
	}

	return <></>;
};
