import type { FC } from "react";
import type { ContentBlock as ContentBlockProps } from "@/payload-types";

import { cn } from "@/lib/utils";
import RichText from "@/components/render/render-rich-text";
import { CMSLink } from "@/components/cms-link";

export const ContentBlock: FC<ContentBlockProps> = (props) => {
	const { columns, indent, id } = props;

	const indentClasses = {
		oneSixth: "lg:col-span-2",
		oneThird: "lg:col-span-4",
		half: "lg:col-span-6",
	};

	return (
		<section className="layout-block">
			<div className="default-grid">
				{indent && indent !== "none" && (
					<div
						className={cn(
							"col-start-1",
							indentClasses[indent as keyof typeof indentClasses],
						)}
					/>
				)}

				{columns?.length > 0 &&
					columns.map((col, index) => {
						const { richText, enableLink, link, width = "4" } = col;

						const colSpan = `col-span-${width}`;

						return (
							<div
								className={cn("col-span-12", "lg:" + colSpan)}
								key={`${id}-${index}`}
							>
								{richText && <RichText data={richText} enableGutter={false} />}
								{enableLink && (
									<CMSLink
										{...link}
										appearance={link?.appearance || undefined}
									/>
								)}
							</div>
						);
					})}
			</div>
		</section>
	);
};
