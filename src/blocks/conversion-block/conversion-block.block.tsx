import { ParallaxImage } from "@/components/motion/parallax-image";
import RichText from "@/components/render/render-rich-text";
import type { ConversionBlock as IConversionBlockProps } from "@/payload-types";
import type { FC } from "react";

export const ConversionBlock: FC<IConversionBlockProps> = async ({
	subtext,
	content,
	variant,
	image,
}) => {
	if (variant === "punk") {
		return (
			<section className="layout-block py-64 relative">
				<RichText data={content} />
				<div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-contrast">
					<img src="/punk.svg" alt="" />
				</div>
			</section>
		);
	}

	return (
		<section className="relative h-[100svh]">
			<div className="absolute inset-0 overflow-hidden">
				<ParallaxImage media={image} />
			</div>
			<div className="layout-block py-12 flex items-end h-full text-primary relative">
				<RichText data={content} enableGutter={false} />
				<p className="absolute right-0 bottom-12 text-right w-columns-4 text-balance">
					{subtext}
				</p>
			</div>
		</section>
	);
};
