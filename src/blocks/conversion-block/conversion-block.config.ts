import {
	BlocksFeature,
	lexicalEditor,
	LinkFeature,
} from "@payloadcms/richtext-lexical";
import type { Block } from "payload";

export const InlineButton: Block = {
	slug: "inline-button",
	interfaceName: "InlineButton",
	fields: [
		{
			type: "row",
			fields: [
				{
					name: "type",
					type: "radio",
					admin: {
						layout: "horizontal",
						width: "50%",
					},
					defaultValue: "reference",
					options: [
						{
							label: "Internal link",
							value: "reference",
						},
						{
							label: "Custom URL",
							value: "custom",
						},
					],
				},
				{
					name: "newTab",
					type: "checkbox",
					admin: {
						style: {
							alignSelf: "flex-end",
						},
						width: "50%",
					},
					label: "Open in new tab",
				},
			],
		},
		{
			type: "row",
			fields: [
				{
					name: "label",
					type: "text",
					admin: {
						width: "50%",
					},
					label: "Label",
					required: true,
				},
				{
					name: "reference",
					type: "relationship",
					admin: {
						condition: (_, siblingData) => siblingData?.type === "reference",
						width: "50%",
					},
					label: "Document to link to",
					relationTo: ["pages"],
					required: true,
				},
				{
					name: "url",
					type: "text",
					admin: {
						condition: (_, siblingData) => siblingData?.type === "custom",
						width: "50%",
					},
					label: "Custom URL",
					required: true,
				},
			],
		},
	],
};

export const ConversionBlockConfig: Block = {
	slug: "conversion",
	interfaceName: "ConversionBlock",
	fields: [
		{
			name: "content",
			type: "richText",
			editor: lexicalEditor({
				features: ({ rootFeatures }) => {
					return [
						...rootFeatures,
						BlocksFeature({
							inlineBlocks: [InlineButton],
						}),
					];
				},
			}),
		},
		{
			name: "subtext",
			type: "text",
		},
		{
			name: "variant",
			type: "select",
			options: [
				{
					label: "Hintergrund bild",
					value: "image",
				},
				{
					label: "Punk Head",
					value: "punk",
				},
			],
			defaultValue: "image",
		},
		{
			name: "image",
			type: "upload",
			relationTo: "media",
			admin: {
				condition: (_, siblingData) => siblingData.variant === "image",
			},
		},
	],
};
