import type { Block } from "payload";

export const ProgramArchiveBlockConfig: Block = {
	slug: "program-archive",
	interfaceName: "ProgramArchiveBlock",
	fields: [
		{
			name: "populateBy",
			type: "select",
			defaultValue: "all",
			options: [
				{
					label: "All",
					value: "all",
				},
				{
					label: "Individual Selection",
					value: "selection",
				},
			],
		},
		{
			name: "limit",
			type: "number",
			admin: {
				condition: (_, siblingData) => siblingData.populateBy === "collection",
				step: 1,
			},
			defaultValue: 10,
			label: "Limit",
		},
		{
			name: "selectedDocs",
			type: "relationship",
			admin: {
				condition: (_, siblingData) => siblingData.populateBy === "selection",
			},
			hasMany: true,
			label: "Selection",
			relationTo: ["programs"],
		},
		{
			name: "sort",
			type: "select",
			options: [
				{
					label: "Newest first",
					value: "DESC",
				},
				{
					label: "Oldest first",
					value: "ASC",
				},
			],
			defaultValue: "newest",
		},
	],
	labels: {
		plural: "Programm Archives",
		singular: "Programm Archive",
	},
};
