import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@/components/accordion";
import type {
	Composer,
	ComposerArchiveBlock as IComposerArchiveBlockProps,
} from "@/payload-types";
import configPromise from "@payload-config";
import { RichText } from "@payloadcms/richtext-lexical/react";
import { getPayload } from "payload";
import type { FC } from "react";

export const ComposerArchiveBlock: FC<
	IComposerArchiveBlockProps
> = async () => {
	const payload = await getPayload({ config: configPromise });

	const composers = await payload.find({
		collection: "composers",
		depth: 1,
		sort: "name",
	});

	const composersByLetter = composers.docs.reduce(
		(acc, composer) => {
			const letter = composer.letter ?? "Z";

			if (!acc[letter]) {
				acc[letter] = [];
			}

			acc[letter].push(composer);

			return acc;
		},
		{} as Record<string, Composer[]>,
	);

	return (
		<section className="layout-block">
			<Accordion type="multiple">
				{Object.entries(composersByLetter).map(([letter, composers]) => (
					<AccordionItem key={letter} value={letter}>
						<AccordionTrigger className="border-b border-secondary">
							{letter}
						</AccordionTrigger>
						<AccordionContent>
							<div className="default-grid border-b border-secondary">
								<div className="col-start-6 col-span-7">
									{composers.map((composer, index) => (
										<div
											key={composer.id}
											className="py-4"
											style={{
												borderBottom:
													index === (composers?.length ?? 0) - 1
														? "none"
														: "1px solid var(--color-secondary)",
											}}
										>
											<p>
												<strong>{composer.name}</strong> - {composer.title}
											</p>
										</div>
									))}
								</div>
							</div>
						</AccordionContent>
					</AccordionItem>
				))}
			</Accordion>
		</section>
	);
};
