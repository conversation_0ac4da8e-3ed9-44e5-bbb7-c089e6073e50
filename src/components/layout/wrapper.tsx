"use client";

import cn from "clsx";
import type { LenisOptions } from "lenis";
// import { type ComponentProps } from "react";

// import type { Theme } from '~/styles/config'
import { Lenis } from "./lenis";
// import { Footer } from "./footer";
// import { Navigation } from "./navigation";

interface WrapperProps extends React.HTMLAttributes<HTMLDivElement> {
	lenis?: boolean | LenisOptions;
	// webgl?: boolean | Omit<ComponentProps<typeof Canvas>, "children">;
}

export function Wrapper({
	children,
	className,
	lenis = true,
	...props
}: WrapperProps) {
	return (
		<>
			{/* <Navigation /> */}
			<main
				className={cn("relative flex grow flex-col !space-y-32", className)}
				{...props}
			>
				{children}
			</main>
			{/* <Footer /> */}
			{lenis && <Lenis root options={typeof lenis === "object" ? lenis : {}} />}
		</>
	);
}
