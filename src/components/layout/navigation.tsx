"use client";

import type { <PERSON> } from "react";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";

const navItems = [
	{ name: "Home", href: "/" },
	{ name: "Work", href: "/work" },
	{ name: "About", href: "/about" },
	{ name: "Services", href: "/services" },
	{ name: "Journal", href: "/journal" },
];

export const Navigation: FC = () => {
	const router = useRouter();
	const pathname = usePathname();

	return (
		<header className="fixed inset-x-0 top-0 z-50 mix-blend-difference text-primary layout-block">
			<div className="default-grid py-safe">
				<Link
					href="/"
					onClick={(e) => {
						e.preventDefault();
						router.push("/");
					}}
					className="relative col-span-4 cursor-pointer"
				>
					<span className="inline-block bg-[#673DC3] px-1 text-sm">
						HARD CHOR
					</span>
				</Link>
				<nav className="col-span-7 col-start-6 flex justify-between">
					<ul className="flex items-center gap-x-[calc(var(--gap)/2)]">
						{navItems.map((item) => (
							<li key={item.name} className="text-sm">
								<Link
									href={item.href}
									variant="inline"
									isActive={item.href === pathname}
									onClick={(e) => {
										e.preventDefault();
										router.push(item.href);
									}}
								>
									{item.name}
								</Link>
							</li>
						))}
					</ul>
					<Link href={"/contact"} className="text-sm">
						Get Started
					</Link>
				</nav>
			</div>
		</header>
	);
};
