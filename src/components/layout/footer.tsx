"use client";

import type { FC } from "react";
import Link from "next/link";
import Image from "next/image";

const items = [
	{
		label: "Konzerte",
		href: "#",
	},
	{
		label: "Sing mit",
		href: "#",
		children: [
			{ label: "Hard-Chor", href: "#" },
			{ label: "Hard-Chor TNG", href: "#" },
		],
	},
	{
		label: "Über uns",
		href: "#",
		children: [
			{ label: "Der Hardchor", href: "#" },
			{ label: "Chorleiter", href: "#" },
			{ label: "Programme", href: "#" },
			{ label: "Reportoire", href: "#" },
			{ label: "Partner & Sponsoren", href: "#" },
		],
	},
	{
		label: "Mediathek",
		href: "#",
		children: [
			{ label: "Pressefotos", href: "#" },
			{ label: "Videos", href: "#" },
			{ label: "Hörpsiele", href: "#" },
			{ label: "Reportoire", href: "#" },
			{ label: "CD", href: "#" },
		],
	},
	{
		label: "Kontakt",
		href: "#",
	},
];

export const Footer: FC = () => {
	return (
		<>
			<section className="relative !-mt-16">
				<div className="pointer-events-none scale-[-1.25] translate-x-1/6">
					<img src="/swirl.svg" alt="swirl" className="w-full h-auto" />
				</div>
			</section>
			<footer className="layout-block pb-8">
				<div className="default-grid">
					<div className="col-span-2">logo</div>
					{items.map((item) => (
						<div className="col-span-1" key={item.label}>
							<Link href={"#"} className="mb-8 inline-block">
								{item.label}
							</Link>
							{item.children?.length && (
								<ul>
									{item.children.map((child) => (
										<li className="mb-4 text-sm" key={child.label}>
											<Link href={child.href}>{child.label}</Link>
										</li>
									))}
								</ul>
							)}
						</div>
					))}
				</div>
				<div className="default-grid mt-32">
					<div className="col-span-2" />
					<div className="col-span-1 whitespace-nowrap">
						<Link href={"#"}>Archiv</Link>
					</div>
					<div className="col-span-1 whitespace-nowrap">
						<Link href={"#"}>Kritiken</Link>
					</div>
					<div className="col-span-1 whitespace-nowrap">
						<Link href={"#"}>Impressum</Link>
					</div>
					<div className="col-span-1 whitespace-nowrap">
						<Link href={"#"}>Datenschutz</Link>
					</div>
					<div className="col-span-1 whitespace-nowrap">
						<Link href={"#"}>Cookie-Richtlinie (EU)</Link>
					</div>
				</div>
			</footer>
		</>
	);
};
