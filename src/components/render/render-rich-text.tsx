import { randId } from "@/lib/utils";
import type { InlineButton } from "@/payload-types";
import type {
	DefaultNodeTypes,
	SerializedLinkNode,
	DefaultTypedEditorState,
	SerializedInlineBlockNode,
} from "@payloadcms/richtext-lexical";
import {
	type JSXConvertersFunction,
	LinkJSXConverter,
	RichText as ConvertRichText,
} from "@payloadcms/richtext-lexical/react";

import cn from "clsx";
import { CMSLink } from "../cms-link";

type NodeTypes = DefaultNodeTypes | SerializedInlineBlockNode<InlineButton>;

const internalDocToHref = ({ linkNode }: { linkNode: SerializedLinkNode }) => {
	const { value, relationTo } = linkNode.fields.doc!;
	if (typeof value !== "object") {
		throw new Error("Expected value to be an object");
	}
	const slug = value.slug;
	return relationTo === "posts" ? `/posts/${slug}` : `/${slug}`;
};

const jsxConverters: JSXConvertersFunction<NodeTypes> = ({
	defaultConverters,
}) => ({
	...defaultConverters,
	inlineBlocks: {
		"inline-button": ({ node }) => {
			const { type, newTab, reference, url, label } = node.fields;

			return (
				<CMSLink
					type={type}
					newTab={newTab}
					reference={reference}
					url={url}
					label={label}
					appearance="inline-button"
				/>
			);
		},
	},
	...LinkJSXConverter({ internalDocToHref }),
});

type Props = {
	data: DefaultTypedEditorState;
	enableGutter?: boolean;
	enableProse?: boolean;
} & React.HTMLAttributes<HTMLDivElement>;

export default function RichText(props: Props) {
	const { className, enableProse = true, enableGutter = true, ...rest } = props;
	const id = randId();

	return (
		<>
			<style>
				{`
				#richtext-${id} br {
					display: none;
				}
			`}
			</style>
			<ConvertRichText
				converters={jsxConverters}
				id={`richtext-${id}`}
				className={cn(
					"payload-richtext",
					{
						container: enableGutter,
						"max-w-none": !enableGutter,
						"prose md:prose-md dark:prose-invert": enableProse,
					},
					className,
				)}
				{...rest}
			/>
		</>
	);
}
