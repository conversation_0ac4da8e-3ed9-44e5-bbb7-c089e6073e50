import type { CollectionConfig } from "payload";

export const Composers: CollectionConfig = {
	slug: "composers",
	admin: {
		useAsTitle: "name",
		defaultColumns: ["name", "title", "updatedAt"],
	},
	fields: [
		{
			name: "name",
			type: "text",
			required: true,
		},
		{
			name: "title",
			type: "text",
		},
		{
			name: "letter",
			type: "text",
			admin: {
				hidden: true,
			},
		},
	],
	hooks: {
		beforeChange: [
			({ data }) => {
				if (data?.name) {
					return {
						...data,
						letter: data.name[0].toUpperCase(),
					};
				}
				return data;
			},
		],
	},
};
